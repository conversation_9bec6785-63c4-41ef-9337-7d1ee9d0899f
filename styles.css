/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    user-select: none;
}

/* Desktop Styles */
.desktop {
    position: relative;
    width: 100vw;
    height: calc(100vh - 40px);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
    overflow: hidden;
}

.desktop-icons {
    position: absolute;
    top: 20px;
    left: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, 80px);
    gap: 20px;
    z-index: 1;
}

.desktop-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.desktop-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.desktop-icon.selected {
    background: rgba(0, 123, 255, 0.3);
    border-color: rgba(0, 123, 255, 0.5);
}

.icon-image {
    font-size: 32px;
    margin-bottom: 5px;
}

.icon-label {
    font-size: 11px;
    color: white;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    word-wrap: break-word;
    max-width: 70px;
}

/* Window Styles */
.windows-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
}

.window {
    position: absolute;
    min-width: 300px;
    min-height: 200px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
    transition: all 0.3s ease;
}

.window.minimized {
    transform: scale(0.1);
    opacity: 0;
    pointer-events: none;
}

.window.maximized {
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: calc(100vh - 40px) !important;
    border-radius: 0;
}

.window-header {
    height: 35px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    padding: 0 15px;
    cursor: move;
}

.window-title {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.window-controls {
    display: flex;
    gap: 8px;
}

.window-control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.window-control.minimize {
    background: #ffbd2e;
}

.window-control.maximize {
    background: #28ca42;
}

.window-control.close {
    background: #ff5f56;
}

.window-control:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.window-content {
    height: calc(100% - 35px);
    padding: 20px;
    overflow: auto;
    background: white;
    position: relative;
}

/* Window Resize Handles */
.resize-handle {
    position: absolute;
    background: transparent;
    z-index: 10;
}

.resize-handle:hover {
    background: rgba(0, 123, 255, 0.2);
}

.resize-n {
    top: -3px;
    left: 10px;
    right: 10px;
    height: 6px;
    cursor: n-resize;
}

.resize-ne {
    top: -3px;
    right: -3px;
    width: 6px;
    height: 6px;
    cursor: ne-resize;
}

.resize-e {
    top: 10px;
    right: -3px;
    bottom: 10px;
    width: 6px;
    cursor: e-resize;
}

.resize-se {
    bottom: -3px;
    right: -3px;
    width: 6px;
    height: 6px;
    cursor: se-resize;
}

.resize-s {
    bottom: -3px;
    left: 10px;
    right: 10px;
    height: 6px;
    cursor: s-resize;
}

.resize-sw {
    bottom: -3px;
    left: -3px;
    width: 6px;
    height: 6px;
    cursor: sw-resize;
}

.resize-w {
    top: 10px;
    left: -3px;
    bottom: 10px;
    width: 6px;
    cursor: w-resize;
}

.resize-nw {
    top: -3px;
    left: -3px;
    width: 6px;
    height: 6px;
    cursor: nw-resize;
}

/* Taskbar Styles */
.taskbar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    z-index: 1000;
}

.start-button {
    height: 100%;
    padding: 0 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.1);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.start-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.start-button.active {
    background: rgba(0, 123, 255, 0.3);
}

.start-icon {
    font-size: 16px;
}

.start-text {
    font-size: 14px;
    color: white;
    font-weight: 500;
}

/* Start Menu Styles */
.start-menu {
    position: absolute;
    bottom: 40px;
    left: 0;
    width: 350px;
    height: 500px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px 12px 0 0;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-bottom: none;
    overflow: hidden;
    transition: all 0.3s ease;
    transform-origin: bottom left;
}

.start-menu.hidden {
    transform: scale(0.8);
    opacity: 0;
    pointer-events: none;
}

.start-menu-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.user-name {
    font-size: 16px;
    font-weight: 500;
}

.start-menu-content {
    padding: 20px;
    height: calc(100% - 140px);
    overflow-y: auto;
}

.app-category h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.app-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.app-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
}

.app-item:hover {
    background: rgba(0, 123, 255, 0.1);
    transform: translateY(-2px);
}

.app-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.app-name {
    font-size: 12px;
    color: #333;
    text-align: center;
}

.start-menu-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.05);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.power-options {
    display: flex;
    gap: 10px;
}

.power-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    color: #666;
}

.power-item:hover {
    background: rgba(0, 0, 0, 0.1);
}

.power-icon {
    font-size: 14px;
}

/* Task List Styles */
.task-list {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 0 10px;
    overflow-x: auto;
}

.task-item {
    height: 30px;
    padding: 0 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 120px;
    max-width: 200px;
}

.task-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.task-item.active {
    background: rgba(0, 123, 255, 0.3);
    border-bottom: 2px solid #007bff;
}

.task-item.multiple-windows {
    position: relative;
}

.task-item.multiple-windows::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    background: #007bff;
    border-radius: 50%;
    border: 1px solid white;
}

.task-icon {
    font-size: 14px;
}

.task-title {
    font-size: 12px;
    color: white;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* System Tray Styles */
.system-tray {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 15px;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.tray-item {
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tray-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.tray-icon {
    font-size: 14px;
    color: white;
}

.clock {
    text-align: center;
    color: white;
    padding: 0 10px;
}

.time {
    font-size: 13px;
    font-weight: 500;
    line-height: 1;
}

.date {
    font-size: 10px;
    opacity: 0.8;
    line-height: 1;
}

/* Context Menu Styles */
.context-menu {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 5px 0;
    min-width: 180px;
    z-index: 10000;
    transition: all 0.2s ease;
}

.context-menu.hidden {
    transform: scale(0.8);
    opacity: 0;
    pointer-events: none;
}

.context-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    color: #333;
}

.context-item:hover {
    background: rgba(0, 123, 255, 0.1);
}

.context-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.context-separator {
    margin: 5px 0;
    border: none;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.no-select {
    user-select: none;
}

.dragging {
    cursor: move !important;
}

/* Loading Screen Styles */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: all 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 80px;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.loading-text {
    font-size: 32px;
    font-weight: 300;
    margin-bottom: 10px;
    letter-spacing: 2px;
}

.loading-subtitle {
    font-size: 16px;
    opacity: 0.8;
    margin-bottom: 30px;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8));
    border-radius: 2px;
    animation: loading 3s ease-in-out;
}

/* Notifications Styles */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 15px;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.notification-icon {
    font-size: 18px;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    flex: 1;
}

.notification-close {
    cursor: pointer;
    font-size: 12px;
    color: #666;
    padding: 2px;
}

.notification-body {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.notification.success {
    border-left: 4px solid #28a745;
}

.notification.warning {
    border-left: 4px solid #ffc107;
}

.notification.error {
    border-left: 4px solid #dc3545;
}

.notification.info {
    border-left: 4px solid #007bff;
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes loading {
    0% { width: 0%; }
    100% { width: 100%; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(-20px); opacity: 0; }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    .start-menu {
        width: 100vw;
        left: 0;
    }

    .desktop-icons {
        grid-template-columns: repeat(auto-fill, 70px);
        gap: 15px;
    }

    .window {
        min-width: 280px;
    }

    .notifications-container {
        left: 10px;
        right: 10px;
        top: 10px;
    }

    .notification {
        min-width: auto;
    }
}
