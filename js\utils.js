// Utility functions for WebOS

class Utils {
    // Generate unique ID
    static generateId() {
        return 'id_' + Math.random().toString(36).substr(2, 9);
    }

    // Format time
    static formatTime(date = new Date()) {
        return date.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: false 
        });
    }

    // Format date
    static formatDate(date = new Date()) {
        return date.toLocaleDateString([], {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    // Debounce function
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Throttle function
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Get element position
    static getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height
        };
    }

    // Check if point is inside element
    static isPointInElement(x, y, element) {
        const rect = element.getBoundingClientRect();
        return x >= rect.left && x <= rect.right && 
               y >= rect.top && y <= rect.bottom;
    }

    // Clamp value between min and max
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }

    // Create element with attributes
    static createElement(tag, attributes = {}, children = []) {
        const element = document.createElement(tag);
        
        Object.keys(attributes).forEach(key => {
            if (key === 'className') {
                element.className = attributes[key];
            } else if (key === 'innerHTML') {
                element.innerHTML = attributes[key];
            } else if (key.startsWith('data-')) {
                element.setAttribute(key, attributes[key]);
            } else {
                element[key] = attributes[key];
            }
        });

        children.forEach(child => {
            if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
            } else {
                element.appendChild(child);
            }
        });

        return element;
    }

    // Show notification
    static showNotification(title, message, type = 'info', duration = 5000) {
        const container = document.getElementById('notifications-container');
        const id = this.generateId();
        
        const notification = this.createElement('div', {
            className: `notification ${type}`,
            id: id
        });

        const header = this.createElement('div', {
            className: 'notification-header'
        });

        const icon = this.createElement('span', {
            className: 'notification-icon',
            innerHTML: this.getNotificationIcon(type)
        });

        const titleEl = this.createElement('span', {
            className: 'notification-title',
            innerHTML: title
        });

        const closeBtn = this.createElement('span', {
            className: 'notification-close',
            innerHTML: '✕'
        });

        const body = this.createElement('div', {
            className: 'notification-body',
            innerHTML: message
        });

        header.appendChild(icon);
        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        notification.appendChild(header);
        notification.appendChild(body);

        container.appendChild(notification);

        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => this.removeNotification(id), duration);
        }

        // Close button handler
        closeBtn.addEventListener('click', () => this.removeNotification(id));

        return id;
    }

    // Remove notification
    static removeNotification(id) {
        const notification = document.getElementById(id);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }

    // Get notification icon
    static getNotificationIcon(type) {
        const icons = {
            success: '✅',
            warning: '⚠️',
            error: '❌',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    // Local storage helpers
    static saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (e) {
            console.error('Failed to save to storage:', e);
            return false;
        }
    }

    static loadFromStorage(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.error('Failed to load from storage:', e);
            return defaultValue;
        }
    }

    // Animation helpers
    static animate(element, properties, duration = 300, easing = 'ease') {
        return new Promise(resolve => {
            const startTime = performance.now();
            const startValues = {};
            const endValues = {};

            // Get start values
            Object.keys(properties).forEach(prop => {
                const computedStyle = getComputedStyle(element);
                startValues[prop] = parseFloat(computedStyle[prop]) || 0;
                endValues[prop] = properties[prop];
            });

            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Apply easing
                let easedProgress = progress;
                if (easing === 'ease-out') {
                    easedProgress = 1 - Math.pow(1 - progress, 3);
                } else if (easing === 'ease-in') {
                    easedProgress = Math.pow(progress, 3);
                }

                // Update properties
                Object.keys(properties).forEach(prop => {
                    const start = startValues[prop];
                    const end = endValues[prop];
                    const current = start + (end - start) * easedProgress;
                    element.style[prop] = current + (prop.includes('opacity') ? '' : 'px');
                });

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            }

            requestAnimationFrame(animate);
        });
    }

    // Event helpers
    static addEventListeners(element, events) {
        Object.keys(events).forEach(event => {
            element.addEventListener(event, events[event]);
        });
    }

    // Drag and drop helpers
    static makeDraggable(element, handle = null, onDrag = null, onDragEnd = null) {
        const dragHandle = handle || element;
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        const onMouseDown = (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(element.style.left) || 0;
            startTop = parseInt(element.style.top) || 0;
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            
            dragHandle.style.cursor = 'grabbing';
            e.preventDefault();
        };

        const onMouseMove = (e) => {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            element.style.left = (startLeft + deltaX) + 'px';
            element.style.top = (startTop + deltaY) + 'px';
            
            if (onDrag) onDrag(deltaX, deltaY);
        };

        const onMouseUp = () => {
            isDragging = false;
            dragHandle.style.cursor = '';
            
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
            
            if (onDragEnd) onDragEnd();
        };

        dragHandle.addEventListener('mousedown', onMouseDown);
        
        return {
            destroy: () => {
                dragHandle.removeEventListener('mousedown', onMouseDown);
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            }
        };
    }
}
