// Desktop Management for WebOS

class Desktop {
    constructor(windowManager, fileSystem, applicationManager) {
        this.windowManager = windowManager;
        this.fileSystem = fileSystem;
        this.applicationManager = applicationManager;
        this.desktop = document.getElementById('desktop');
        this.iconsContainer = document.getElementById('desktop-icons');
        this.contextMenu = document.getElementById('context-menu');
        this.startButton = document.getElementById('start-button');
        this.startMenu = document.getElementById('start-menu');
        this.clock = document.getElementById('clock');
        this.isStartMenuOpen = false;
        this.eventListenersAdded = false;

        this.init();
    }

    init() {
        this.setupDesktopIcons();
        this.setupContextMenu();
        this.setupStartMenu();
        this.setupTaskbar();
        this.startClock();
        this.setupKeyboardShortcuts();
        this.eventListenersAdded = true;
    }

    setupDesktopIcons() {
        const desktopFiles = this.fileSystem.getFiles('/Desktop');
        
        desktopFiles.forEach(file => {
            this.createDesktopIcon(file);
        });
    }

    createDesktopIcon(file) {
        const icon = Utils.createElement('div', {
            className: 'desktop-icon',
            'data-path': file.path,
            'data-type': file.type
        });

        const iconImage = Utils.createElement('div', {
            className: 'icon-image',
            innerHTML: this.fileSystem.getFileIcon(file)
        });

        const iconLabel = Utils.createElement('div', {
            className: 'icon-label',
            innerHTML: file.name.replace('.lnk', '')
        });

        icon.appendChild(iconImage);
        icon.appendChild(iconLabel);

        // Make icon draggable
        Utils.makeDraggable(icon, icon, null, () => {
            this.saveIconPosition(file.path, icon);
        });

        // Double-click to open with debounce
        icon.addEventListener('dblclick', Utils.debounce(() => {
            this.openDesktopItem(file);
        }, 300));

        // Single click to select
        icon.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectIcon(icon);
        });

        this.iconsContainer.appendChild(icon);
        
        // Load saved position
        this.loadIconPosition(file.path, icon);
    }

    openDesktopItem(file) {
        // Prevent multiple rapid opens
        if (this.lastOpenTime && Date.now() - this.lastOpenTime < 1000) {
            return;
        }
        this.lastOpenTime = Date.now();

        if (file.type === 'shortcut') {
            this.applicationManager.launchApplication(file.target);
        } else if (file.type === 'text') {
            this.applicationManager.launchTextEditor(file.path);
        } else if (file.type === 'folder') {
            this.applicationManager.launchFileManager(file.path);
        }
    }

    selectIcon(icon) {
        // Deselect all icons
        this.iconsContainer.querySelectorAll('.desktop-icon').forEach(i => {
            i.classList.remove('selected');
        });
        
        // Select this icon
        icon.classList.add('selected');
    }

    saveIconPosition(filePath, icon) {
        const positions = Utils.loadFromStorage('desktop-icon-positions', {});
        positions[filePath] = {
            left: parseInt(icon.style.left) || 0,
            top: parseInt(icon.style.top) || 0
        };
        Utils.saveToStorage('desktop-icon-positions', positions);
    }

    loadIconPosition(filePath, icon) {
        const positions = Utils.loadFromStorage('desktop-icon-positions', {});
        const position = positions[filePath];
        
        if (position) {
            icon.style.left = position.left + 'px';
            icon.style.top = position.top + 'px';
        }
    }

    setupContextMenu() {
        if (this.eventListenersAdded) return;

        // Right-click on desktop
        this.desktop.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e.clientX, e.clientY);
        });

        // Hide context menu on click elsewhere
        document.addEventListener('click', () => {
            this.hideContextMenu();
        });

        // Context menu actions
        this.contextMenu.addEventListener('click', (e) => {
            const action = e.target.closest('.context-item')?.dataset.action;
            if (action) {
                this.handleContextAction(action);
                this.hideContextMenu();
            }
        });
    }

    showContextMenu(x, y) {
        this.contextMenu.style.left = x + 'px';
        this.contextMenu.style.top = y + 'px';
        this.contextMenu.classList.remove('hidden');
    }

    hideContextMenu() {
        this.contextMenu.classList.add('hidden');
    }

    handleContextAction(action) {
        switch (action) {
            case 'refresh':
                this.refreshDesktop();
                break;
            case 'new-folder':
                this.createNewFolder();
                break;
            case 'new-file':
                this.createNewFile();
                break;
            case 'properties':
                this.showDesktopProperties();
                break;
        }
    }

    refreshDesktop() {
        // Clear current icons and their event listeners
        const existingIcons = this.iconsContainer.querySelectorAll('.desktop-icon');
        existingIcons.forEach(icon => {
            // Remove all event listeners by cloning the element
            const newIcon = icon.cloneNode(true);
            icon.parentNode.replaceChild(newIcon, icon);
        });

        // Clear container
        this.iconsContainer.innerHTML = '';

        // Reload icons
        this.setupDesktopIcons();

        Utils.showNotification('Desktop', 'Desktop refreshed', 'info');
    }

    createNewFolder() {
        const name = prompt('Enter folder name:');
        if (name) {
            try {
                this.fileSystem.createFolder('/Desktop', name);
                this.refreshDesktop();
                Utils.showNotification('Success', 'Folder created successfully', 'success');
            } catch (e) {
                Utils.showNotification('Error', e.message, 'error');
            }
        }
    }

    createNewFile() {
        const name = prompt('Enter file name:');
        if (name) {
            try {
                this.fileSystem.createTextFile('/Desktop', name);
                this.refreshDesktop();
                Utils.showNotification('Success', 'File created successfully', 'success');
            } catch (e) {
                Utils.showNotification('Error', e.message, 'error');
            }
        }
    }

    showDesktopProperties() {
        this.applicationManager.launchApplication('settings');
    }

    setupStartMenu() {
        if (this.startMenuListenersAdded) return;

        // Start button click
        this.startButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleStartMenu();
        });

        // App item clicks
        this.startMenu.addEventListener('click', (e) => {
            const appItem = e.target.closest('.app-item');
            if (appItem) {
                const appId = appItem.dataset.app;
                this.applicationManager.launchApplication(appId);
                this.hideStartMenu();
            }
        });

        // Power options
        this.startMenu.addEventListener('click', (e) => {
            const powerItem = e.target.closest('.power-item');
            if (powerItem) {
                const action = powerItem.dataset.action;
                this.handlePowerAction(action);
            }
        });

        // Hide start menu when clicking elsewhere
        document.addEventListener('click', (e) => {
            if (!this.startButton.contains(e.target) && !this.startMenu.contains(e.target)) {
                this.hideStartMenu();
            }
        });

        this.startMenuListenersAdded = true;
    }

    toggleStartMenu() {
        if (this.isStartMenuOpen) {
            this.hideStartMenu();
        } else {
            this.showStartMenu();
        }
    }

    showStartMenu() {
        this.startMenu.classList.remove('hidden');
        this.startButton.classList.add('active');
        this.isStartMenuOpen = true;
    }

    hideStartMenu() {
        this.startMenu.classList.add('hidden');
        this.startButton.classList.remove('active');
        this.isStartMenuOpen = false;
    }

    handlePowerAction(action) {
        switch (action) {
            case 'shutdown':
                this.shutdown();
                break;
            case 'restart':
                this.restart();
                break;
        }
        this.hideStartMenu();
    }

    shutdown() {
        Utils.showNotification('System', 'Shutting down...', 'info');
        
        // Close all windows
        this.windowManager.getAllWindows().forEach(window => {
            this.windowManager.closeWindow(window.id);
        });
        
        // Show shutdown screen
        setTimeout(() => {
            document.body.innerHTML = `
                <div style="
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: #000; color: white; display: flex;
                    align-items: center; justify-content: center;
                    font-family: Arial, sans-serif; font-size: 24px;
                ">
                    <div style="text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 20px;">⏻</div>
                        <div>System Shutdown</div>
                        <div style="font-size: 16px; margin-top: 10px; opacity: 0.7;">
                            It's now safe to close this tab
                        </div>
                    </div>
                </div>
            `;
        }, 2000);
    }

    restart() {
        Utils.showNotification('System', 'Restarting...', 'info');
        
        // Close all windows
        this.windowManager.getAllWindows().forEach(window => {
            this.windowManager.closeWindow(window.id);
        });
        
        // Reload page
        setTimeout(() => {
            location.reload();
        }, 2000);
    }

    setupTaskbar() {
        if (this.taskbarListenersAdded) return;

        // System tray items
        document.getElementById('notifications').addEventListener('click', () => {
            Utils.showNotification('System', 'Notification center opened', 'info');
        });

        document.getElementById('volume').addEventListener('click', () => {
            Utils.showNotification('Audio', 'Volume control opened', 'info');
        });

        document.getElementById('network').addEventListener('click', () => {
            Utils.showNotification('Network', 'Network settings opened', 'info');
        });

        this.taskbarListenersAdded = true;
    }

    startClock() {
        const updateClock = () => {
            const now = new Date();
            const timeEl = this.clock.querySelector('.time');
            const dateEl = this.clock.querySelector('.date');
            
            timeEl.textContent = Utils.formatTime(now);
            dateEl.textContent = Utils.formatDate(now);
        };

        updateClock();
        setInterval(updateClock, 1000);
    }

    setupKeyboardShortcuts() {
        if (this.keyboardListenersAdded) return;

        document.addEventListener('keydown', (e) => {
            // Ctrl+Alt+T - Open Terminal
            if (e.ctrlKey && e.altKey && e.key === 't') {
                e.preventDefault();
                this.applicationManager.launchApplication('terminal');
            }

            // Ctrl+Alt+F - Open File Manager
            if (e.ctrlKey && e.altKey && e.key === 'f') {
                e.preventDefault();
                this.applicationManager.launchApplication('file-manager');
            }

            // Ctrl+Alt+C - Open Calculator
            if (e.ctrlKey && e.altKey && e.key === 'c') {
                e.preventDefault();
                this.applicationManager.launchApplication('calculator');
            }

            // Windows key - Toggle Start Menu
            if (e.key === 'Meta' || e.key === 'OS') {
                e.preventDefault();
                this.toggleStartMenu();
            }

            // Alt+F4 - Close active window
            if (e.altKey && e.key === 'F4') {
                e.preventDefault();
                if (this.windowManager.activeWindow) {
                    this.windowManager.closeWindow(this.windowManager.activeWindow);
                }
            }

            // Escape - Hide context menu and start menu
            if (e.key === 'Escape') {
                this.hideContextMenu();
                this.hideStartMenu();
            }
        });

        this.keyboardListenersAdded = true;
    }

    // Desktop click handler
    setupDesktopClick() {
        this.desktop.addEventListener('click', (e) => {
            if (e.target === this.desktop) {
                // Deselect all icons
                this.iconsContainer.querySelectorAll('.desktop-icon').forEach(icon => {
                    icon.classList.remove('selected');
                });
            }
        });
    }

    // Wallpaper management
    changeWallpaper(imageUrl) {
        this.desktop.style.backgroundImage = `url(${imageUrl})`;
        this.desktop.style.backgroundSize = 'cover';
        this.desktop.style.backgroundPosition = 'center';
        
        // Save preference
        Utils.saveToStorage('wallpaper', imageUrl);
    }

    loadWallpaper() {
        const wallpaper = Utils.loadFromStorage('wallpaper');
        if (wallpaper) {
            this.changeWallpaper(wallpaper);
        }
    }

    // Theme management
    applyTheme(theme) {
        document.body.className = `theme-${theme}`;
        Utils.saveToStorage('theme', theme);
    }

    loadTheme() {
        const theme = Utils.loadFromStorage('theme', 'default');
        this.applyTheme(theme);
    }
}
