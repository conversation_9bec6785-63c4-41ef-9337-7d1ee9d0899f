// Main application entry point for WebOS

class WebOS {
    constructor() {
        this.windowManager = null;
        this.fileSystem = null;
        this.applicationManager = null;
        this.desktop = null;
        this.isLoaded = false;
        
        this.init();
    }

    async init() {
        try {
            // Show loading screen
            this.showLoadingScreen();
            
            // Initialize core systems
            await this.initializeSystems();
            
            // Load user preferences
            this.loadUserPreferences();
            
            // Hide loading screen and show desktop
            await this.hideLoadingScreen();
            
            // Show welcome notification
            this.showWelcomeMessage();
            
            this.isLoaded = true;
            
        } catch (error) {
            console.error('Failed to initialize WebOS:', error);
            this.showErrorScreen(error);
        }
    }

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const progressBar = loadingScreen.querySelector('.loading-progress');
        
        // Animate progress bar
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
            }
            progressBar.style.width = progress + '%';
        }, 200);
    }

    async initializeSystems() {
        // Initialize file system
        await this.delay(300);
        this.fileSystem = new FileSystem();
        this.updateLoadingText('Initializing file system...');
        
        // Initialize window manager
        await this.delay(300);
        this.windowManager = new WindowManager();
        this.updateLoadingText('Setting up window manager...');
        
        // Initialize application manager
        await this.delay(300);
        this.applicationManager = new ApplicationManager(this.windowManager, this.fileSystem);
        this.updateLoadingText('Loading applications...');
        
        // Initialize desktop
        await this.delay(300);
        this.desktop = new Desktop(this.windowManager, this.fileSystem, this.applicationManager);
        this.updateLoadingText('Preparing desktop...');
        
        await this.delay(500);
    }

    updateLoadingText(text) {
        const loadingSubtitle = document.querySelector('.loading-subtitle');
        if (loadingSubtitle) {
            loadingSubtitle.textContent = text;
        }
    }

    async hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        
        // Fade out loading screen
        loadingScreen.style.transition = 'opacity 0.5s ease';
        loadingScreen.style.opacity = '0';
        
        await this.delay(500);
        
        loadingScreen.classList.add('hidden');
    }

    showWelcomeMessage() {
        const isFirstTime = !Utils.loadFromStorage('webos-initialized');
        
        if (isFirstTime) {
            Utils.showNotification(
                'Welcome to WebOS!',
                'Your web-based operating system is ready. Try opening applications from the Start menu or double-clicking desktop icons.',
                'success',
                8000
            );
            
            Utils.saveToStorage('webos-initialized', true);
        } else {
            Utils.showNotification(
                'Welcome back!',
                'WebOS has loaded successfully.',
                'info',
                3000
            );
        }
    }

    showErrorScreen(error) {
        document.body.innerHTML = `
            <div style="
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                color: white; display: flex; align-items: center; justify-content: center;
                font-family: Arial, sans-serif;
            ">
                <div style="text-align: center; max-width: 500px; padding: 40px;">
                    <div style="font-size: 64px; margin-bottom: 20px;">⚠️</div>
                    <h1 style="font-size: 32px; margin-bottom: 20px;">System Error</h1>
                    <p style="font-size: 16px; margin-bottom: 20px; opacity: 0.9;">
                        WebOS failed to initialize properly. Please refresh the page to try again.
                    </p>
                    <div style="
                        background: rgba(0,0,0,0.2); padding: 15px; border-radius: 8px;
                        font-family: monospace; font-size: 12px; text-align: left;
                        margin-bottom: 20px;
                    ">
                        ${error.message || 'Unknown error occurred'}
                    </div>
                    <button onclick="location.reload()" style="
                        background: white; color: #ee5a24; border: none; padding: 12px 24px;
                        border-radius: 6px; font-size: 16px; cursor: pointer; font-weight: bold;
                    ">
                        Refresh Page
                    </button>
                </div>
            </div>
        `;
    }

    loadUserPreferences() {
        // Load theme
        const theme = Utils.loadFromStorage('theme', 'default');
        this.applyTheme(theme);
        
        // Load wallpaper
        const wallpaper = Utils.loadFromStorage('wallpaper');
        if (wallpaper) {
            this.desktop.changeWallpaper(wallpaper);
        }
        
        // Load window positions (if any windows were saved)
        this.loadSavedWindows();
    }

    applyTheme(themeName) {
        document.body.setAttribute('data-theme', themeName);
        
        // Apply theme-specific styles
        const themes = {
            dark: {
                '--bg-primary': '#1a1a1a',
                '--bg-secondary': '#2d2d2d',
                '--text-primary': '#ffffff',
                '--text-secondary': '#cccccc',
                '--accent': '#007bff'
            },
            light: {
                '--bg-primary': '#ffffff',
                '--bg-secondary': '#f8f9fa',
                '--text-primary': '#333333',
                '--text-secondary': '#666666',
                '--accent': '#007bff'
            }
        };
        
        const themeVars = themes[themeName];
        if (themeVars) {
            Object.keys(themeVars).forEach(property => {
                document.documentElement.style.setProperty(property, themeVars[property]);
            });
        }
    }

    loadSavedWindows() {
        const savedWindows = Utils.loadFromStorage('saved-windows', []);
        
        savedWindows.forEach(windowData => {
            try {
                if (windowData.appId) {
                    const windowId = this.applicationManager.launchApplication(windowData.appId);
                    
                    // Restore window position and size
                    setTimeout(() => {
                        const window = this.windowManager.getWindow(windowId);
                        if (window && windowData.bounds) {
                            const { x, y, width, height } = windowData.bounds;
                            window.element.style.left = x + 'px';
                            window.element.style.top = y + 'px';
                            window.element.style.width = width + 'px';
                            window.element.style.height = height + 'px';
                        }
                    }, 100);
                }
            } catch (error) {
                console.warn('Failed to restore window:', error);
            }
        });
    }

    saveSession() {
        const windows = this.windowManager.getAllWindows();
        const savedWindows = windows.map(window => ({
            appId: this.getAppIdFromWindow(window),
            bounds: {
                x: parseInt(window.element.style.left),
                y: parseInt(window.element.style.top),
                width: parseInt(window.element.style.width),
                height: parseInt(window.element.style.height)
            }
        })).filter(w => w.appId);
        
        Utils.saveToStorage('saved-windows', savedWindows);
    }

    getAppIdFromWindow(window) {
        // Try to determine app ID from window title or other properties
        const title = window.config.title.toLowerCase();
        
        if (title.includes('file manager')) return 'file-manager';
        if (title.includes('text editor')) return 'text-editor';
        if (title.includes('calculator')) return 'calculator';
        if (title.includes('image viewer')) return 'image-viewer';
        if (title.includes('settings')) return 'settings';
        if (title.includes('terminal')) return 'terminal';
        
        return null;
    }

    // Utility method for delays
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // System shutdown/restart handlers
    shutdown() {
        this.saveSession();
        this.desktop.shutdown();
    }

    restart() {
        this.saveSession();
        this.desktop.restart();
    }

    // Global error handler
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            Utils.showNotification(
                'System Error',
                'An unexpected error occurred. Check the console for details.',
                'error'
            );
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            Utils.showNotification(
                'System Warning',
                'An operation failed to complete properly.',
                'warning'
            );
        });
    }

    // Performance monitoring
    setupPerformanceMonitoring() {
        // Monitor memory usage (if available)
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
                const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
                
                // Warn if memory usage is high
                if (usedMB > 100) {
                    console.warn(`High memory usage: ${usedMB}MB / ${totalMB}MB`);
                }
            }, 30000); // Check every 30 seconds
        }
    }

    // Auto-save functionality
    setupAutoSave() {
        // Save session every 5 minutes
        setInterval(() => {
            this.saveSession();
        }, 300000);
        
        // Save on page unload
        window.addEventListener('beforeunload', () => {
            this.saveSession();
        });
    }
}

// Initialize WebOS when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create global WebOS instance
    window.webOS = new WebOS();
    
    // Setup global error handling
    window.webOS.setupErrorHandling();
    
    // Setup performance monitoring
    window.webOS.setupPerformanceMonitoring();
    
    // Setup auto-save
    window.webOS.setupAutoSave();
});

// Prevent default browser behaviors that might interfere
document.addEventListener('contextmenu', (e) => {
    // Allow context menu only on specific elements
    if (!e.target.closest('.text-editor-content, .terminal-input')) {
        e.preventDefault();
    }
});

document.addEventListener('dragover', (e) => {
    e.preventDefault();
});

document.addEventListener('drop', (e) => {
    e.preventDefault();
});

// Handle browser back/forward buttons
window.addEventListener('popstate', (e) => {
    e.preventDefault();
    // Prevent navigation away from WebOS
});

// Console welcome message
console.log(`
╔══════════════════════════════════════╗
║              WebOS v1.0              ║
║     Web-based Operating System       ║
║                                      ║
║  Built with HTML, CSS & JavaScript   ║
║                                      ║
║  Available commands:                 ║
║  - webOS.shutdown()                  ║
║  - webOS.restart()                   ║
║  - webOS.saveSession()               ║
╚══════════════════════════════════════╝
`);
