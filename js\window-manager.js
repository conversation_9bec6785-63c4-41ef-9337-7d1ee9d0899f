// Window Management System for WebOS

class WindowManager {
    constructor() {
        this.windows = new Map();
        this.activeWindow = null;
        this.zIndexCounter = 100;
        this.container = document.getElementById('windows-container');
        this.taskList = document.getElementById('task-list');
        
        this.init();
    }

    init() {
        // <PERSON>le clicks outside windows to deactivate
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.window') && !e.target.closest('.task-item')) {
                this.deactivateAllWindows();
            }
        });
    }

    createWindow(options = {}) {
        const windowId = Utils.generateId();
        const defaultOptions = {
            title: 'Untitled Window',
            width: 600,
            height: 400,
            x: 100,
            y: 100,
            resizable: true,
            minimizable: true,
            maximizable: true,
            closable: true,
            content: '',
            icon: '📄'
        };

        const config = { ...defaultOptions, ...options };
        
        // Create window element
        const windowEl = Utils.createElement('div', {
            className: 'window',
            id: windowId
        });

        // Create window header
        const header = this.createWindowHeader(config);
        
        // Create window content
        const content = Utils.createElement('div', {
            className: 'window-content',
            innerHTML: config.content
        });

        windowEl.appendChild(header);
        windowEl.appendChild(content);

        // Set initial position and size
        windowEl.style.left = config.x + 'px';
        windowEl.style.top = config.y + 'px';
        windowEl.style.width = config.width + 'px';
        windowEl.style.height = config.height + 'px';
        windowEl.style.zIndex = ++this.zIndexCounter;

        // Add to container
        this.container.appendChild(windowEl);

        // Create window object
        const windowObj = {
            id: windowId,
            element: windowEl,
            header: header,
            content: content,
            config: config,
            isMinimized: false,
            isMaximized: false,
            originalBounds: null
        };

        // Store window
        this.windows.set(windowId, windowObj);

        // Setup window interactions
        this.setupWindowInteractions(windowObj);

        // Add to task list
        this.addToTaskList(windowObj);

        // Activate window
        this.activateWindow(windowId);

        // Show window with animation
        setTimeout(() => {
            windowEl.style.opacity = '1';
            windowEl.style.transform = 'scale(1)';
        }, 10);

        return windowId;
    }

    createWindowHeader(config) {
        const header = Utils.createElement('div', {
            className: 'window-header'
        });

        const title = Utils.createElement('div', {
            className: 'window-title',
            innerHTML: config.title
        });

        const controls = Utils.createElement('div', {
            className: 'window-controls'
        });

        if (config.minimizable) {
            const minimizeBtn = Utils.createElement('div', {
                className: 'window-control minimize',
                'data-action': 'minimize'
            });
            controls.appendChild(minimizeBtn);
        }

        if (config.maximizable) {
            const maximizeBtn = Utils.createElement('div', {
                className: 'window-control maximize',
                'data-action': 'maximize'
            });
            controls.appendChild(maximizeBtn);
        }

        if (config.closable) {
            const closeBtn = Utils.createElement('div', {
                className: 'window-control close',
                'data-action': 'close'
            });
            controls.appendChild(closeBtn);
        }

        header.appendChild(title);
        header.appendChild(controls);

        return header;
    }

    setupWindowInteractions(windowObj) {
        const { element, header, config } = windowObj;

        // Make window draggable
        Utils.makeDraggable(element, header, null, () => {
            this.activateWindow(windowObj.id);
        });

        // Window control buttons
        header.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                e.stopPropagation();
                this.handleWindowAction(windowObj.id, action);
            }
        });

        // Activate window on click
        element.addEventListener('mousedown', () => {
            this.activateWindow(windowObj.id);
        });

        // Double-click header to maximize/restore
        header.addEventListener('dblclick', () => {
            if (config.maximizable) {
                this.handleWindowAction(windowObj.id, 'maximize');
            }
        });

        // Make window resizable
        if (config.resizable) {
            this.makeResizable(windowObj);
        }
    }

    makeResizable(windowObj) {
        const { element } = windowObj;
        const resizeHandles = ['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'];
        
        resizeHandles.forEach(direction => {
            const handle = Utils.createElement('div', {
                className: `resize-handle resize-${direction}`,
                style: `position: absolute; ${this.getResizeHandleStyle(direction)}`
            });
            
            element.appendChild(handle);
            
            Utils.makeDraggable(handle, handle, (deltaX, deltaY) => {
                this.resizeWindow(windowObj, direction, deltaX, deltaY);
            });
        });
    }

    getResizeHandleStyle(direction) {
        const styles = {
            n: 'top: -3px; left: 10px; right: 10px; height: 6px; cursor: n-resize;',
            ne: 'top: -3px; right: -3px; width: 6px; height: 6px; cursor: ne-resize;',
            e: 'top: 10px; right: -3px; bottom: 10px; width: 6px; cursor: e-resize;',
            se: 'bottom: -3px; right: -3px; width: 6px; height: 6px; cursor: se-resize;',
            s: 'bottom: -3px; left: 10px; right: 10px; height: 6px; cursor: s-resize;',
            sw: 'bottom: -3px; left: -3px; width: 6px; height: 6px; cursor: sw-resize;',
            w: 'top: 10px; left: -3px; bottom: 10px; width: 6px; cursor: w-resize;',
            nw: 'top: -3px; left: -3px; width: 6px; height: 6px; cursor: nw-resize;'
        };
        return styles[direction];
    }

    resizeWindow(windowObj, direction, deltaX, deltaY) {
        const { element, config } = windowObj;
        const rect = element.getBoundingClientRect();
        
        let newWidth = rect.width;
        let newHeight = rect.height;
        let newLeft = rect.left;
        let newTop = rect.top;

        if (direction.includes('e')) newWidth += deltaX;
        if (direction.includes('w')) { newWidth -= deltaX; newLeft += deltaX; }
        if (direction.includes('s')) newHeight += deltaY;
        if (direction.includes('n')) { newHeight -= deltaY; newTop += deltaY; }

        // Apply constraints
        newWidth = Utils.clamp(newWidth, 300, window.innerWidth);
        newHeight = Utils.clamp(newHeight, 200, window.innerHeight - 40);

        element.style.width = newWidth + 'px';
        element.style.height = newHeight + 'px';
        element.style.left = newLeft + 'px';
        element.style.top = newTop + 'px';
    }

    handleWindowAction(windowId, action) {
        const windowObj = this.windows.get(windowId);
        if (!windowObj) return;

        switch (action) {
            case 'minimize':
                this.minimizeWindow(windowId);
                break;
            case 'maximize':
                this.maximizeWindow(windowId);
                break;
            case 'close':
                this.closeWindow(windowId);
                break;
        }
    }

    minimizeWindow(windowId) {
        const windowObj = this.windows.get(windowId);
        if (!windowObj) return;

        windowObj.element.classList.add('minimized');
        windowObj.isMinimized = true;
        
        this.updateTaskItem(windowObj);
        
        if (this.activeWindow === windowId) {
            this.activeWindow = null;
        }
    }

    maximizeWindow(windowId) {
        const windowObj = this.windows.get(windowId);
        if (!windowObj) return;

        if (windowObj.isMaximized) {
            // Restore
            windowObj.element.classList.remove('maximized');
            if (windowObj.originalBounds) {
                const { left, top, width, height } = windowObj.originalBounds;
                windowObj.element.style.left = left + 'px';
                windowObj.element.style.top = top + 'px';
                windowObj.element.style.width = width + 'px';
                windowObj.element.style.height = height + 'px';
            }
            windowObj.isMaximized = false;
        } else {
            // Maximize
            windowObj.originalBounds = {
                left: parseInt(windowObj.element.style.left),
                top: parseInt(windowObj.element.style.top),
                width: parseInt(windowObj.element.style.width),
                height: parseInt(windowObj.element.style.height)
            };
            windowObj.element.classList.add('maximized');
            windowObj.isMaximized = true;
        }
    }

    closeWindow(windowId) {
        const windowObj = this.windows.get(windowId);
        if (!windowObj) return;

        // Remove from DOM
        windowObj.element.remove();
        
        // Remove from task list
        this.removeFromTaskList(windowObj);
        
        // Remove from windows map
        this.windows.delete(windowId);
        
        // Update active window
        if (this.activeWindow === windowId) {
            this.activeWindow = null;
        }
    }

    activateWindow(windowId) {
        const windowObj = this.windows.get(windowId);
        if (!windowObj) return;

        // Deactivate all windows
        this.deactivateAllWindows();

        // Activate this window
        windowObj.element.style.zIndex = ++this.zIndexCounter;
        windowObj.element.classList.add('active');
        this.activeWindow = windowId;

        // Restore if minimized
        if (windowObj.isMinimized) {
            windowObj.element.classList.remove('minimized');
            windowObj.isMinimized = false;
        }

        this.updateTaskItem(windowObj);
    }

    deactivateAllWindows() {
        this.windows.forEach(windowObj => {
            windowObj.element.classList.remove('active');
        });
        this.updateAllTaskItems();
    }

    addToTaskList(windowObj) {
        const taskItem = Utils.createElement('div', {
            className: 'task-item',
            'data-window-id': windowObj.id
        });

        const icon = Utils.createElement('span', {
            className: 'task-icon',
            innerHTML: windowObj.config.icon
        });

        const title = Utils.createElement('span', {
            className: 'task-title',
            innerHTML: windowObj.config.title
        });

        taskItem.appendChild(icon);
        taskItem.appendChild(title);

        // Click handler
        taskItem.addEventListener('click', () => {
            if (this.activeWindow === windowObj.id && !windowObj.isMinimized) {
                this.minimizeWindow(windowObj.id);
            } else {
                this.activateWindow(windowObj.id);
            }
        });

        this.taskList.appendChild(taskItem);
        windowObj.taskItem = taskItem;
    }

    removeFromTaskList(windowObj) {
        if (windowObj.taskItem) {
            windowObj.taskItem.remove();
        }
    }

    updateTaskItem(windowObj) {
        if (!windowObj.taskItem) return;

        windowObj.taskItem.classList.toggle('active', 
            this.activeWindow === windowObj.id && !windowObj.isMinimized);
    }

    updateAllTaskItems() {
        this.windows.forEach(windowObj => {
            this.updateTaskItem(windowObj);
        });
    }

    getWindow(windowId) {
        return this.windows.get(windowId);
    }

    getAllWindows() {
        return Array.from(this.windows.values());
    }
}
