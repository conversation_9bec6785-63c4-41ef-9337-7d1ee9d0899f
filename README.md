# WebOS - Web-Based Operating System

A fully functional web-based operating system built with HTML, CSS, and JavaScript. Experience a complete desktop environment running entirely in your browser!

## 🌟 Features

### Desktop Environment
- **Modern UI Design** - Clean, professional interface with glassmorphism effects
- **Desktop Icons** - Draggable desktop shortcuts with persistent positioning
- **Taskbar** - Functional taskbar with start menu, running applications, and system tray
- **Context Menus** - Right-click context menus for desktop and file operations
- **Wallpaper Support** - Customizable desktop backgrounds

### Window Management
- **Multi-Window Support** - Open multiple applications simultaneously
- **Window Controls** - Minimize, maximize, and close windows
- **Drag & Drop** - Move windows around the desktop
- **Resizable Windows** - Resize windows from any edge or corner
- **Window Stacking** - Proper z-index management for overlapping windows

### File System
- **Virtual File System** - Complete file and folder management
- **File Operations** - Create, delete, rename files and folders
- **File Types** - Support for text files, folders, and application shortcuts
- **Persistent Storage** - Files saved in browser local storage

### Applications
- **📁 File Manager** - Browse and manage files and folders
- **📝 Text Editor** - Create and edit text documents with auto-save
- **🧮 Calculator** - Fully functional calculator with standard operations
- **🖼️ Image Viewer** - View and manage images
- **⚙️ Settings** - System configuration and preferences
- **💻 Terminal** - Command-line interface with basic commands

### System Features
- **Notifications** - System-wide notification system
- **Keyboard Shortcuts** - Productivity shortcuts for common actions
- **Theme Support** - Multiple themes (default, dark, light)
- **Auto-Save** - Automatic session and data persistence
- **Loading Screen** - Professional boot sequence
- **Error Handling** - Graceful error management and recovery

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No additional software or server required!

### Installation
1. Download or clone this repository
2. Open `index.html` in your web browser
3. Wait for the system to load
4. Enjoy your web-based operating system!

### File Structure
```
webos/
├── index.html          # Main HTML file
├── styles.css          # Complete CSS styling
├── js/
│   ├── main.js         # Application entry point
│   ├── utils.js        # Utility functions
│   ├── window-manager.js # Window management system
│   ├── file-system.js  # Virtual file system
│   ├── applications.js # Built-in applications
│   └── desktop.js      # Desktop environment
└── README.md           # This file
```

## 🎮 How to Use

### Basic Operations
- **Start Menu**: Click the "Start" button or press the Windows key
- **Open Applications**: Click on desktop icons or use the start menu
- **Window Management**: Drag windows by their title bar, use window controls
- **File Management**: Double-click the File Manager icon to browse files
- **Context Menu**: Right-click on the desktop for options

### Keyboard Shortcuts
- `Ctrl+Alt+T` - Open Terminal
- `Ctrl+Alt+F` - Open File Manager
- `Ctrl+Alt+C` - Open Calculator
- `Alt+F4` - Close active window
- `Windows Key` - Toggle Start Menu
- `Escape` - Close menus and dialogs

### Applications Guide

#### File Manager
- Navigate folders by double-clicking
- Create new files and folders using toolbar buttons
- Open text files in the Text Editor

#### Text Editor
- Create new documents or open existing ones
- Auto-save functionality preserves your work
- Use toolbar buttons for file operations

#### Calculator
- Standard arithmetic operations
- Click buttons or use keyboard input
- Supports decimal calculations

#### Terminal
- Basic command-line interface
- Available commands: `help`, `clear`, `date`, `echo`, `ls`, `pwd`
- Type `help` for a list of available commands

## 🎨 Customization

### Themes
Access the Settings application to change themes:
- **Default**: Blue gradient theme
- **Dark**: Dark mode for low-light environments
- **Light**: Clean light theme

### Desktop
- Drag icons to rearrange them
- Right-click for context menu options
- Create new files and folders on the desktop

## 🔧 Technical Details

### Architecture
- **Modular Design**: Separate modules for different system components
- **Event-Driven**: Responsive UI with proper event handling
- **Local Storage**: Persistent data using browser storage APIs
- **No Dependencies**: Pure vanilla JavaScript, no external libraries

### Browser Compatibility
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Performance
- Optimized for smooth animations and interactions
- Memory usage monitoring and warnings
- Efficient DOM manipulation and event handling

## 🐛 Known Issues

- File uploads from local system not implemented
- Audio/video file support limited
- Print functionality not available
- Network operations simulated only

## 🚧 Future Enhancements

- [ ] File upload/download functionality
- [ ] More built-in applications (music player, games)
- [ ] Network simulation and web browsing
- [ ] Multi-user support
- [ ] Plugin system for third-party applications
- [ ] Mobile responsive design
- [ ] Cloud storage integration

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Feel free to:
- Report bugs and issues
- Suggest new features
- Submit pull requests
- Improve documentation

## 🙏 Acknowledgments

- Inspired by classic desktop operating systems
- Built with modern web technologies
- Designed for educational and demonstration purposes

---

**WebOS** - Bringing the desktop experience to the web! 🌐💻
