// Virtual File System for WebOS

class FileSystem {
    constructor() {
        this.files = new Map();
        this.currentPath = '/';
        this.init();
    }

    init() {
        // Create default file structure
        this.createDefaultStructure();
    }

    createDefaultStructure() {
        // Root directory
        this.createFile('/', 'folder', {
            name: 'Root',
            children: ['Desktop', 'Documents', 'Pictures', 'Downloads']
        });

        // Desktop
        this.createFile('/Desktop', 'folder', {
            name: 'Desktop',
            children: ['File Manager.lnk', 'Text Editor.lnk', 'Calculator.lnk', 'Welcome.txt']
        });

        // Documents
        this.createFile('/Documents', 'folder', {
            name: 'Documents',
            children: ['Sample Document.txt', 'Notes.txt']
        });

        // Pictures
        this.createFile('/Pictures', 'folder', {
            name: 'Pictures',
            children: []
        });

        // Downloads
        this.createFile('/Downloads', 'folder', {
            name: 'Downloads',
            children: []
        });

        // Desktop shortcuts
        this.createFile('/Desktop/File Manager.lnk', 'shortcut', {
            name: 'File Manager',
            target: 'file-manager',
            icon: '📁'
        });

        this.createFile('/Desktop/Text Editor.lnk', 'shortcut', {
            name: 'Text Editor',
            target: 'text-editor',
            icon: '📝'
        });

        this.createFile('/Desktop/Calculator.lnk', 'shortcut', {
            name: 'Calculator',
            target: 'calculator',
            icon: '🧮'
        });

        // Sample files
        this.createFile('/Desktop/Welcome.txt', 'text', {
            name: 'Welcome.txt',
            content: 'Welcome to WebOS!\n\nThis is a web-based operating system built with HTML, CSS, and JavaScript.\n\nFeatures:\n- Window management\n- File system\n- Applications\n- Desktop environment\n\nEnjoy exploring!'
        });

        this.createFile('/Documents/Sample Document.txt', 'text', {
            name: 'Sample Document.txt',
            content: 'This is a sample document.\n\nYou can edit this text using the Text Editor application.'
        });

        this.createFile('/Documents/Notes.txt', 'text', {
            name: 'Notes.txt',
            content: 'My Notes:\n\n- Remember to save important files\n- Try the calculator app\n- Explore the file manager\n- Customize the desktop'
        });
    }

    createFile(path, type, data) {
        const file = {
            path: path,
            type: type,
            name: data.name,
            created: new Date(),
            modified: new Date(),
            size: this.calculateSize(data),
            ...data
        };

        this.files.set(path, file);
        return file;
    }

    calculateSize(data) {
        if (data.content) {
            return data.content.length;
        } else if (data.children) {
            return data.children.length * 100; // Approximate folder size
        }
        return 0;
    }

    getFile(path) {
        return this.files.get(path);
    }

    getFiles(directoryPath = '/') {
        const directory = this.files.get(directoryPath);
        if (!directory || directory.type !== 'folder') {
            return [];
        }

        return directory.children.map(childName => {
            const childPath = this.joinPath(directoryPath, childName);
            return this.files.get(childPath);
        }).filter(file => file);
    }

    createFolder(parentPath, name) {
        const folderPath = this.joinPath(parentPath, name);
        
        if (this.files.has(folderPath)) {
            throw new Error('Folder already exists');
        }

        const folder = this.createFile(folderPath, 'folder', {
            name: name,
            children: []
        });

        // Add to parent directory
        const parent = this.files.get(parentPath);
        if (parent && parent.type === 'folder') {
            parent.children.push(name);
            parent.modified = new Date();
        }

        return folder;
    }

    createTextFile(parentPath, name, content = '') {
        const filePath = this.joinPath(parentPath, name);
        
        if (this.files.has(filePath)) {
            throw new Error('File already exists');
        }

        const file = this.createFile(filePath, 'text', {
            name: name,
            content: content
        });

        // Add to parent directory
        const parent = this.files.get(parentPath);
        if (parent && parent.type === 'folder') {
            parent.children.push(name);
            parent.modified = new Date();
        }

        return file;
    }

    deleteFile(path) {
        const file = this.files.get(path);
        if (!file) {
            throw new Error('File not found');
        }

        // Remove from parent directory
        const parentPath = this.getParentPath(path);
        const parent = this.files.get(parentPath);
        if (parent && parent.type === 'folder') {
            const index = parent.children.indexOf(file.name);
            if (index > -1) {
                parent.children.splice(index, 1);
                parent.modified = new Date();
            }
        }

        // If it's a folder, delete all children
        if (file.type === 'folder') {
            file.children.forEach(childName => {
                const childPath = this.joinPath(path, childName);
                this.deleteFile(childPath);
            });
        }

        this.files.delete(path);
        return true;
    }

    renameFile(path, newName) {
        const file = this.files.get(path);
        if (!file) {
            throw new Error('File not found');
        }

        const parentPath = this.getParentPath(path);
        const newPath = this.joinPath(parentPath, newName);

        if (this.files.has(newPath)) {
            throw new Error('File with new name already exists');
        }

        // Update parent directory
        const parent = this.files.get(parentPath);
        if (parent && parent.type === 'folder') {
            const index = parent.children.indexOf(file.name);
            if (index > -1) {
                parent.children[index] = newName;
                parent.modified = new Date();
            }
        }

        // Update file
        file.name = newName;
        file.path = newPath;
        file.modified = new Date();

        // Move in map
        this.files.delete(path);
        this.files.set(newPath, file);

        return file;
    }

    saveFile(path, content) {
        const file = this.files.get(path);
        if (!file) {
            throw new Error('File not found');
        }

        if (file.type !== 'text') {
            throw new Error('Cannot save non-text file');
        }

        file.content = content;
        file.modified = new Date();
        file.size = content.length;

        return file;
    }

    joinPath(parent, child) {
        if (parent === '/') {
            return '/' + child;
        }
        return parent + '/' + child;
    }

    getParentPath(path) {
        if (path === '/') {
            return '/';
        }
        const parts = path.split('/');
        parts.pop();
        return parts.join('/') || '/';
    }

    getFileName(path) {
        const parts = path.split('/');
        return parts[parts.length - 1];
    }

    getFileIcon(file) {
        const icons = {
            folder: '📁',
            text: '📄',
            shortcut: '🔗',
            image: '🖼️',
            audio: '🎵',
            video: '🎬',
            archive: '📦'
        };

        if (file.icon) {
            return file.icon;
        }

        return icons[file.type] || '📄';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    formatDate(date) {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    search(query, directory = '/') {
        const results = [];
        const searchRecursive = (path) => {
            const files = this.getFiles(path);
            files.forEach(file => {
                if (file.name.toLowerCase().includes(query.toLowerCase())) {
                    results.push(file);
                }
                if (file.type === 'folder') {
                    searchRecursive(file.path);
                }
            });
        };

        searchRecursive(directory);
        return results;
    }

    exportData() {
        const data = {};
        this.files.forEach((file, path) => {
            data[path] = file;
        });
        return JSON.stringify(data, null, 2);
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            this.files.clear();
            
            Object.keys(data).forEach(path => {
                const file = data[path];
                file.created = new Date(file.created);
                file.modified = new Date(file.modified);
                this.files.set(path, file);
            });
            
            return true;
        } catch (e) {
            console.error('Failed to import data:', e);
            return false;
        }
    }
}
