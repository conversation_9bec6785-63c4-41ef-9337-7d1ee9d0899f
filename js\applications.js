// Applications for WebOS

class ApplicationManager {
    constructor(windowManager, fileSystem) {
        this.windowManager = windowManager;
        this.fileSystem = fileSystem;
        this.applications = new Map();
        this.init();
    }

    init() {
        this.registerApplications();
    }

    registerApplications() {
        // File Manager
        this.applications.set('file-manager', {
            name: 'File Manager',
            icon: '📁',
            launch: () => this.launchFileManager()
        });

        // Text Editor
        this.applications.set('text-editor', {
            name: 'Text Editor',
            icon: '📝',
            launch: (filePath) => this.launchTextEditor(filePath)
        });

        // Calculator
        this.applications.set('calculator', {
            name: 'Calculator',
            icon: '🧮',
            launch: () => this.launchCalculator()
        });

        // Image Viewer
        this.applications.set('image-viewer', {
            name: 'Image Viewer',
            icon: '🖼️',
            launch: () => this.launchImageViewer()
        });

        // Settings
        this.applications.set('settings', {
            name: 'Settings',
            icon: '⚙️',
            launch: () => this.launchSettings()
        });

        // Terminal
        this.applications.set('terminal', {
            name: 'Terminal',
            icon: '💻',
            launch: () => this.launchTerminal()
        });
    }

    launchApplication(appId, ...args) {
        const app = this.applications.get(appId);
        if (app) {
            return app.launch(...args);
        } else {
            Utils.showNotification('Error', `Application '${appId}' not found`, 'error');
        }
    }

    launchFileManager(initialPath = '/') {
        const content = this.createFileManagerContent(initialPath);
        
        const windowId = this.windowManager.createWindow({
            title: 'File Manager',
            icon: '📁',
            width: 800,
            height: 600,
            content: content
        });

        this.setupFileManagerEvents(windowId, initialPath);
        return windowId;
    }

    createFileManagerContent(currentPath) {
        const files = this.fileSystem.getFiles(currentPath);
        
        return `
            <div class="file-manager">
                <div class="file-manager-toolbar">
                    <button class="btn btn-back" data-action="back">← Back</button>
                    <button class="btn btn-up" data-action="up">↑ Up</button>
                    <div class="path-bar">
                        <input type="text" class="path-input" value="${currentPath}" readonly>
                    </div>
                    <button class="btn btn-new-folder" data-action="new-folder">📁 New Folder</button>
                    <button class="btn btn-new-file" data-action="new-file">📄 New File</button>
                </div>
                
                <div class="file-manager-content">
                    <div class="file-list">
                        ${files.map(file => `
                            <div class="file-item" data-path="${file.path}" data-type="${file.type}">
                                <div class="file-icon">${this.fileSystem.getFileIcon(file)}</div>
                                <div class="file-info">
                                    <div class="file-name">${file.name}</div>
                                    <div class="file-details">
                                        ${file.type === 'folder' ? 'Folder' : this.fileSystem.formatFileSize(file.size)}
                                        • ${this.fileSystem.formatDate(file.modified)}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <div class="file-manager-status">
                    <span>${files.length} items</span>
                </div>
            </div>
            
            <style>
                .file-manager { height: 100%; display: flex; flex-direction: column; }
                .file-manager-toolbar { display: flex; gap: 10px; padding: 10px; border-bottom: 1px solid #ddd; align-items: center; }
                .btn { padding: 5px 10px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer; }
                .btn:hover { background: #f0f0f0; }
                .path-bar { flex: 1; }
                .path-input { width: 100%; padding: 5px; border: 1px solid #ddd; border-radius: 4px; }
                .file-manager-content { flex: 1; overflow: auto; }
                .file-list { padding: 10px; }
                .file-item { display: flex; align-items: center; gap: 10px; padding: 8px; border-radius: 4px; cursor: pointer; }
                .file-item:hover { background: #f0f0f0; }
                .file-item.selected { background: #e3f2fd; }
                .file-icon { font-size: 24px; }
                .file-info { flex: 1; }
                .file-name { font-weight: 500; }
                .file-details { font-size: 12px; color: #666; }
                .file-manager-status { padding: 5px 10px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
            </style>
        `;
    }

    setupFileManagerEvents(windowId, currentPath) {
        const window = this.windowManager.getWindow(windowId);
        const content = window.content;

        // File item clicks
        content.addEventListener('click', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                const path = fileItem.dataset.path;
                const type = fileItem.dataset.type;
                
                if (type === 'folder') {
                    // Navigate to folder
                    this.updateFileManagerContent(windowId, path);
                } else if (type === 'shortcut') {
                    // Launch application
                    const file = this.fileSystem.getFile(path);
                    if (file.target) {
                        this.launchApplication(file.target);
                    }
                } else if (type === 'text') {
                    // Open in text editor
                    this.launchTextEditor(path);
                }
            }
        });

        // Toolbar actions
        content.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleFileManagerAction(windowId, action, currentPath);
            }
        });
    }

    updateFileManagerContent(windowId, newPath) {
        const window = this.windowManager.getWindow(windowId);
        window.content.innerHTML = this.createFileManagerContent(newPath);
        this.setupFileManagerEvents(windowId, newPath);
    }

    handleFileManagerAction(windowId, action, currentPath) {
        switch (action) {
            case 'up':
                const parentPath = this.fileSystem.getParentPath(currentPath);
                if (parentPath !== currentPath) {
                    this.updateFileManagerContent(windowId, parentPath);
                }
                break;
            case 'new-folder':
                const folderName = prompt('Enter folder name:');
                if (folderName) {
                    try {
                        this.fileSystem.createFolder(currentPath, folderName);
                        this.updateFileManagerContent(windowId, currentPath);
                        Utils.showNotification('Success', 'Folder created successfully', 'success');
                    } catch (e) {
                        Utils.showNotification('Error', e.message, 'error');
                    }
                }
                break;
            case 'new-file':
                const fileName = prompt('Enter file name:');
                if (fileName) {
                    try {
                        this.fileSystem.createTextFile(currentPath, fileName);
                        this.updateFileManagerContent(windowId, currentPath);
                        Utils.showNotification('Success', 'File created successfully', 'success');
                    } catch (e) {
                        Utils.showNotification('Error', e.message, 'error');
                    }
                }
                break;
        }
    }

    launchTextEditor(filePath = null) {
        let content = '';
        let fileName = 'Untitled';
        
        if (filePath) {
            const file = this.fileSystem.getFile(filePath);
            if (file && file.type === 'text') {
                content = file.content;
                fileName = file.name;
            }
        }

        const editorContent = `
            <div class="text-editor">
                <div class="text-editor-toolbar">
                    <button class="btn btn-new" data-action="new">New</button>
                    <button class="btn btn-open" data-action="open">Open</button>
                    <button class="btn btn-save" data-action="save">Save</button>
                    <span class="file-name">${fileName}</span>
                </div>
                <textarea class="text-editor-content" placeholder="Start typing...">${content}</textarea>
            </div>
            
            <style>
                .text-editor { height: 100%; display: flex; flex-direction: column; }
                .text-editor-toolbar { display: flex; gap: 10px; padding: 10px; border-bottom: 1px solid #ddd; align-items: center; }
                .file-name { margin-left: auto; font-weight: 500; }
                .text-editor-content { flex: 1; border: none; outline: none; padding: 20px; font-family: 'Courier New', monospace; resize: none; }
            </style>
        `;

        const windowId = this.windowManager.createWindow({
            title: `Text Editor - ${fileName}`,
            icon: '📝',
            width: 700,
            height: 500,
            content: editorContent
        });

        this.setupTextEditorEvents(windowId, filePath);
        return windowId;
    }

    setupTextEditorEvents(windowId, currentFilePath) {
        const window = this.windowManager.getWindow(windowId);
        const content = window.content;
        const textarea = content.querySelector('.text-editor-content');

        content.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleTextEditorAction(windowId, action, currentFilePath);
            }
        });

        // Auto-save on content change
        textarea.addEventListener('input', Utils.debounce(() => {
            if (currentFilePath) {
                try {
                    this.fileSystem.saveFile(currentFilePath, textarea.value);
                } catch (e) {
                    console.error('Auto-save failed:', e);
                }
            }
        }, 1000));
    }

    handleTextEditorAction(windowId, action, currentFilePath) {
        const window = this.windowManager.getWindow(windowId);
        const textarea = window.content.querySelector('.text-editor-content');

        switch (action) {
            case 'new':
                textarea.value = '';
                window.header.querySelector('.window-title').textContent = 'Text Editor - Untitled';
                break;
            case 'save':
                if (currentFilePath) {
                    try {
                        this.fileSystem.saveFile(currentFilePath, textarea.value);
                        Utils.showNotification('Success', 'File saved successfully', 'success');
                    } catch (e) {
                        Utils.showNotification('Error', e.message, 'error');
                    }
                } else {
                    const fileName = prompt('Enter file name:');
                    if (fileName) {
                        try {
                            this.fileSystem.createTextFile('/Documents', fileName, textarea.value);
                            Utils.showNotification('Success', 'File saved successfully', 'success');
                        } catch (e) {
                            Utils.showNotification('Error', e.message, 'error');
                        }
                    }
                }
                break;
        }
    }

    launchCalculator() {
        const calculatorContent = `
            <div class="calculator">
                <div class="calculator-display">
                    <input type="text" class="display" value="0" readonly>
                </div>
                <div class="calculator-buttons">
                    <button class="btn btn-clear" data-action="clear">C</button>
                    <button class="btn btn-operator" data-value="/">/</button>
                    <button class="btn btn-operator" data-value="*">×</button>
                    <button class="btn btn-operator" data-value="-">-</button>
                    
                    <button class="btn btn-number" data-value="7">7</button>
                    <button class="btn btn-number" data-value="8">8</button>
                    <button class="btn btn-number" data-value="9">9</button>
                    <button class="btn btn-operator" data-value="+">+</button>
                    
                    <button class="btn btn-number" data-value="4">4</button>
                    <button class="btn btn-number" data-value="5">5</button>
                    <button class="btn btn-number" data-value="6">6</button>
                    <button class="btn btn-equals" data-action="equals" rowspan="2">=</button>
                    
                    <button class="btn btn-number" data-value="1">1</button>
                    <button class="btn btn-number" data-value="2">2</button>
                    <button class="btn btn-number" data-value="3">3</button>
                    
                    <button class="btn btn-number btn-zero" data-value="0">0</button>
                    <button class="btn btn-number" data-value=".">.</button>
                </div>
            </div>
            
            <style>
                .calculator { padding: 20px; }
                .calculator-display { margin-bottom: 15px; }
                .display { width: 100%; padding: 15px; font-size: 24px; text-align: right; border: 1px solid #ddd; border-radius: 4px; }
                .calculator-buttons { display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; }
                .btn { padding: 20px; font-size: 18px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer; }
                .btn:hover { background: #f0f0f0; }
                .btn-operator { background: #007bff; color: white; }
                .btn-equals { background: #28a745; color: white; grid-row: span 2; }
                .btn-zero { grid-column: span 2; }
            </style>
        `;

        const windowId = this.windowManager.createWindow({
            title: 'Calculator',
            icon: '🧮',
            width: 300,
            height: 400,
            content: calculatorContent
        });

        this.setupCalculatorEvents(windowId);
        return windowId;
    }

    setupCalculatorEvents(windowId) {
        const window = this.windowManager.getWindow(windowId);
        const content = window.content;
        const display = content.querySelector('.display');
        let currentValue = '0';
        let operator = null;
        let previousValue = null;
        let waitingForOperand = false;

        content.addEventListener('click', (e) => {
            const target = e.target;
            
            if (target.classList.contains('btn-number')) {
                const value = target.dataset.value;
                if (waitingForOperand) {
                    currentValue = value;
                    waitingForOperand = false;
                } else {
                    currentValue = currentValue === '0' ? value : currentValue + value;
                }
                display.value = currentValue;
            }
            
            if (target.classList.contains('btn-operator')) {
                const nextOperator = target.dataset.value;
                
                if (previousValue === null) {
                    previousValue = currentValue;
                } else if (operator) {
                    const result = this.calculate(previousValue, currentValue, operator);
                    currentValue = String(result);
                    display.value = currentValue;
                    previousValue = currentValue;
                }
                
                waitingForOperand = true;
                operator = nextOperator;
            }
            
            if (target.dataset.action === 'equals') {
                if (previousValue !== null && operator) {
                    const result = this.calculate(previousValue, currentValue, operator);
                    currentValue = String(result);
                    display.value = currentValue;
                    previousValue = null;
                    operator = null;
                    waitingForOperand = true;
                }
            }
            
            if (target.dataset.action === 'clear') {
                currentValue = '0';
                previousValue = null;
                operator = null;
                waitingForOperand = false;
                display.value = currentValue;
            }
        });
    }

    calculate(firstOperand, secondOperand, operator) {
        const first = parseFloat(firstOperand);
        const second = parseFloat(secondOperand);
        
        switch (operator) {
            case '+': return first + second;
            case '-': return first - second;
            case '*': return first * second;
            case '/': return first / second;
            default: return second;
        }
    }

    launchImageViewer() {
        const content = `
            <div class="image-viewer">
                <div class="image-viewer-toolbar">
                    <button class="btn" data-action="open">Open Image</button>
                    <button class="btn" data-action="zoom-in">Zoom In</button>
                    <button class="btn" data-action="zoom-out">Zoom Out</button>
                    <button class="btn" data-action="fit">Fit to Window</button>
                </div>
                <div class="image-viewer-content">
                    <div class="image-placeholder">
                        <div class="placeholder-icon">🖼️</div>
                        <div class="placeholder-text">No image loaded</div>
                        <div class="placeholder-subtext">Click "Open Image" to load an image</div>
                    </div>
                </div>
            </div>
            
            <style>
                .image-viewer { height: 100%; display: flex; flex-direction: column; }
                .image-viewer-toolbar { display: flex; gap: 10px; padding: 10px; border-bottom: 1px solid #ddd; }
                .image-viewer-content { flex: 1; display: flex; align-items: center; justify-content: center; overflow: auto; }
                .image-placeholder { text-align: center; color: #666; }
                .placeholder-icon { font-size: 64px; margin-bottom: 20px; }
                .placeholder-text { font-size: 18px; margin-bottom: 10px; }
                .placeholder-subtext { font-size: 14px; }
            </style>
        `;

        return this.windowManager.createWindow({
            title: 'Image Viewer',
            icon: '🖼️',
            width: 600,
            height: 500,
            content: content
        });
    }

    launchSettings() {
        const content = `
            <div class="settings">
                <div class="settings-sidebar">
                    <div class="settings-category active" data-category="appearance">🎨 Appearance</div>
                    <div class="settings-category" data-category="system">⚙️ System</div>
                    <div class="settings-category" data-category="about">ℹ️ About</div>
                </div>
                <div class="settings-content">
                    <div class="settings-panel" data-panel="appearance">
                        <h3>Appearance Settings</h3>
                        <div class="setting-item">
                            <label>Theme:</label>
                            <select class="theme-select">
                                <option value="default">Default</option>
                                <option value="dark">Dark</option>
                                <option value="light">Light</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>Desktop Background:</label>
                            <input type="color" class="bg-color" value="#667eea">
                        </div>
                    </div>
                    <div class="settings-panel hidden" data-panel="system">
                        <h3>System Settings</h3>
                        <div class="setting-item">
                            <label>Auto-save:</label>
                            <input type="checkbox" checked>
                        </div>
                        <div class="setting-item">
                            <label>Notifications:</label>
                            <input type="checkbox" checked>
                        </div>
                    </div>
                    <div class="settings-panel hidden" data-panel="about">
                        <h3>About WebOS</h3>
                        <p>WebOS v1.0</p>
                        <p>A web-based operating system built with HTML, CSS, and JavaScript.</p>
                        <p>© 2024 WebOS Project</p>
                    </div>
                </div>
            </div>
            
            <style>
                .settings { height: 100%; display: flex; }
                .settings-sidebar { width: 200px; border-right: 1px solid #ddd; padding: 20px 0; }
                .settings-category { padding: 10px 20px; cursor: pointer; border-left: 3px solid transparent; }
                .settings-category:hover { background: #f0f0f0; }
                .settings-category.active { background: #e3f2fd; border-left-color: #007bff; }
                .settings-content { flex: 1; padding: 20px; }
                .settings-panel h3 { margin-bottom: 20px; }
                .setting-item { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; }
                .setting-item label { min-width: 120px; }
            </style>
        `;

        return this.windowManager.createWindow({
            title: 'Settings',
            icon: '⚙️',
            width: 600,
            height: 500,
            content: content
        });
    }

    launchTerminal() {
        const content = `
            <div class="terminal">
                <div class="terminal-output">
                    <div class="terminal-line">WebOS Terminal v1.0</div>
                    <div class="terminal-line">Type 'help' for available commands.</div>
                    <div class="terminal-line"></div>
                </div>
                <div class="terminal-input-line">
                    <span class="terminal-prompt">user@webos:~$ </span>
                    <input type="text" class="terminal-input" autofocus>
                </div>
            </div>
            
            <style>
                .terminal { height: 100%; background: #000; color: #00ff00; font-family: 'Courier New', monospace; padding: 20px; display: flex; flex-direction: column; }
                .terminal-output { flex: 1; overflow-y: auto; }
                .terminal-line { margin-bottom: 5px; }
                .terminal-input-line { display: flex; align-items: center; }
                .terminal-prompt { color: #00ff00; }
                .terminal-input { background: transparent; border: none; outline: none; color: #00ff00; font-family: inherit; flex: 1; }
            </style>
        `;

        const windowId = this.windowManager.createWindow({
            title: 'Terminal',
            icon: '💻',
            width: 600,
            height: 400,
            content: content
        });

        this.setupTerminalEvents(windowId);
        return windowId;
    }

    setupTerminalEvents(windowId) {
        const window = this.windowManager.getWindow(windowId);
        const content = window.content;
        const input = content.querySelector('.terminal-input');
        const output = content.querySelector('.terminal-output');

        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const command = input.value.trim();
                this.executeTerminalCommand(command, output);
                input.value = '';
            }
        });
    }

    executeTerminalCommand(command, output) {
        const line = document.createElement('div');
        line.className = 'terminal-line';
        line.textContent = `user@webos:~$ ${command}`;
        output.appendChild(line);

        const response = document.createElement('div');
        response.className = 'terminal-line';

        switch (command.toLowerCase()) {
            case 'help':
                response.textContent = 'Available commands: help, clear, date, echo, ls, pwd';
                break;
            case 'clear':
                output.innerHTML = '';
                return;
            case 'date':
                response.textContent = new Date().toString();
                break;
            case 'ls':
                response.textContent = 'Desktop  Documents  Pictures  Downloads';
                break;
            case 'pwd':
                response.textContent = '/home/<USER>';
                break;
            default:
                if (command.startsWith('echo ')) {
                    response.textContent = command.substring(5);
                } else if (command) {
                    response.textContent = `Command not found: ${command}`;
                } else {
                    return;
                }
        }

        output.appendChild(response);
        output.scrollTop = output.scrollHeight;
    }
}
