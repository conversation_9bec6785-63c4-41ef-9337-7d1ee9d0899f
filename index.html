<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebOS - Web Operating System</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💻</text></svg>">
</head>
<body>
    <!-- Desktop Container -->
    <div id="desktop" class="desktop">
        <!-- Desktop Icons Container -->
        <div id="desktop-icons" class="desktop-icons">
            <!-- Desktop icons will be dynamically added here -->
        </div>
        
        <!-- Windows Container -->
        <div id="windows-container" class="windows-container">
            <!-- Application windows will be dynamically added here -->
        </div>
        
        <!-- Context Menu -->
        <div id="context-menu" class="context-menu hidden">
            <div class="context-item" data-action="refresh">
                <span class="context-icon">🔄</span>
                <span>Refresh</span>
            </div>
            <div class="context-item" data-action="new-folder">
                <span class="context-icon">📁</span>
                <span>New Folder</span>
            </div>
            <div class="context-item" data-action="new-file">
                <span class="context-icon">📄</span>
                <span>New File</span>
            </div>
            <hr class="context-separator">
            <div class="context-item" data-action="properties">
                <span class="context-icon">⚙️</span>
                <span>Properties</span>
            </div>
        </div>
    </div>
    
    <!-- Taskbar -->
    <div id="taskbar" class="taskbar">
        <!-- Start Button -->
        <div id="start-button" class="start-button">
            <span class="start-icon">🏠</span>
            <span class="start-text">Start</span>
        </div>
        
        <!-- Start Menu -->
        <div id="start-menu" class="start-menu hidden">
            <div class="start-menu-header">
                <div class="user-info">
                    <div class="user-avatar">👤</div>
                    <div class="user-name">User</div>
                </div>
            </div>
            
            <div class="start-menu-content">
                <div class="app-category">
                    <h3>Applications</h3>
                    <div class="app-grid">
                        <div class="app-item" data-app="file-manager">
                            <span class="app-icon">📁</span>
                            <span class="app-name">File Manager</span>
                        </div>
                        <div class="app-item" data-app="text-editor">
                            <span class="app-icon">📝</span>
                            <span class="app-name">Text Editor</span>
                        </div>
                        <div class="app-item" data-app="calculator">
                            <span class="app-icon">🧮</span>
                            <span class="app-name">Calculator</span>
                        </div>
                        <div class="app-item" data-app="image-viewer">
                            <span class="app-icon">🖼️</span>
                            <span class="app-name">Image Viewer</span>
                        </div>
                        <div class="app-item" data-app="settings">
                            <span class="app-icon">⚙️</span>
                            <span class="app-name">Settings</span>
                        </div>
                        <div class="app-item" data-app="terminal">
                            <span class="app-icon">💻</span>
                            <span class="app-name">Terminal</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="start-menu-footer">
                <div class="power-options">
                    <div class="power-item" data-action="shutdown">
                        <span class="power-icon">⏻</span>
                        <span>Shutdown</span>
                    </div>
                    <div class="power-item" data-action="restart">
                        <span class="power-icon">🔄</span>
                        <span>Restart</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Task List -->
        <div id="task-list" class="task-list">
            <!-- Running applications will appear here -->
        </div>
        
        <!-- System Tray -->
        <div id="system-tray" class="system-tray">
            <div class="tray-item" id="notifications" title="Notifications">
                <span class="tray-icon">🔔</span>
            </div>
            <div class="tray-item" id="volume" title="Volume">
                <span class="tray-icon">🔊</span>
            </div>
            <div class="tray-item" id="network" title="Network">
                <span class="tray-icon">📶</span>
            </div>
            
            <!-- Clock -->
            <div id="clock" class="clock">
                <div class="time">12:00</div>
                <div class="date">01/01/2024</div>
            </div>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notifications-container" class="notifications-container">
        <!-- Notifications will be dynamically added here -->
    </div>
    
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">💻</div>
            <div class="loading-text">WebOS</div>
            <div class="loading-subtitle">Starting up...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/window-manager.js"></script>
    <script src="js/file-system.js"></script>
    <script src="js/applications.js"></script>
    <script src="js/desktop.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
